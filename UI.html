<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说人物关系管理器 - UI设计展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f6f6f6;
            min-height: 100vh;
            padding: 40px 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .title {
            font-size: 32px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .description {
            font-size: 14px;
            color: #888;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .ui-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .ui-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .ui-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }
        
        .ui-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .ui-item-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .ui-item-badge {
            padding: 4px 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .ui-item-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        
        .iframe-container {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            border: 1px solid #e1e5e9;
        }
        
        .ui-iframe {
            width: 400px;
            height: 850px;
            border: none;
            display: block;
            background: white;
        }
        
        .ui-features {
            margin-top: 12px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .feature-tag {
            padding: 4px 8px;
            background: #f1f5f9;
            color: #667eea;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .stats-section {
            background: white;
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 40px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stats-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .footer {
            text-align: center;
            margin-top: 60px;
            padding-top: 40px;
            border-top: 1px solid #e1e5e9;
        }
        
        .footer-text {
            font-size: 14px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .footer-copyright {
            font-size: 12px;
            color: #ccc;
        }
        
        @media (max-width: 768px) {
            .ui-grid {
                grid-template-columns: 1fr;
            }
            
            .ui-iframe {
                width: 100%;
                max-width: 400px;
            }
            
            .title {
                font-size: 24px;
            }
            
            body {
                padding: 20px 10px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">小说人物关系管理器</h1>
        <p class="subtitle">Novel Relationship Manager - UI设计展示</p>
        <p class="description">
            专为作家、编剧和文学爱好者设计的小说人物关系管理工具。通过可视化的方式管理复杂的人物关系网络，
            提供直观的关系图谱、时间线管理和笔记系统，让创作更加高效有序。
        </p>
    </div>
    
    <div class="stats-section">
        <h2 class="stats-title">设计概览</h2>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">9</div>
                <div class="stat-label">核心页面</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">功能模块</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">400×850</div>
                <div class="stat-label">移动端尺寸</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">响应式设计</div>
            </div>
        </div>
    </div>
    
    <div class="ui-grid">
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">登录页面</h3>
                <span class="ui-item-badge">认证</span>
            </div>
            <p class="ui-item-description">用户身份验证和应用入口，采用现代简约设计风格，包含登录表单、注册链接和忘记密码功能。</p>
            <div class="iframe-container">
                <iframe src="login.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">渐变背景</span>
                <span class="feature-tag">卡片设计</span>
                <span class="feature-tag">表单验证</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">注册页面</h3>
                <span class="ui-item-badge">认证</span>
            </div>
            <p class="ui-item-description">新用户账户创建界面，包含完整的注册流程、密码强度指示器和用户协议确认。</p>
            <div class="iframe-container">
                <iframe src="register.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">密码强度</span>
                <span class="feature-tag">协议确认</span>
                <span class="feature-tag">表单验证</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">控制面板</h3>
                <span class="ui-item-badge">核心</span>
            </div>
            <p class="ui-item-description">应用主入口，展示用户数据概览、统计信息、快速操作入口和最近活动动态。</p>
            <div class="iframe-container">
                <iframe src="dashboard.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">数据统计</span>
                <span class="feature-tag">快速操作</span>
                <span class="feature-tag">活动动态</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">小说列表</h3>
                <span class="ui-item-badge">管理</span>
            </div>
            <p class="ui-item-description">展示和管理用户的所有小说作品，支持搜索筛选、状态管理和快速创建功能。</p>
            <div class="iframe-container">
                <iframe src="novels.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">搜索筛选</span>
                <span class="feature-tag">状态管理</span>
                <span class="feature-tag">卡片布局</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">小说详情</h3>
                <span class="ui-item-badge">详情</span>
            </div>
            <p class="ui-item-description">单个小说的详细信息展示，包含基本信息、统计数据、快速操作和内容预览。</p>
            <div class="iframe-container">
                <iframe src="novel-detail.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">信息展示</span>
                <span class="feature-tag">快速操作</span>
                <span class="feature-tag">内容预览</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">角色管理</h3>
                <span class="ui-item-badge">核心</span>
            </div>
            <p class="ui-item-description">管理小说中的所有角色，支持角色分类、搜索筛选和详细信息展示。</p>
            <div class="iframe-container">
                <iframe src="characters.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">角色分类</span>
                <span class="feature-tag">网格布局</span>
                <span class="feature-tag">统计信息</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">关系图谱</h3>
                <span class="ui-item-badge">可视化</span>
            </div>
            <p class="ui-item-description">交互式关系网络图，可视化展示角色之间的复杂关系，支持拖拽、缩放和筛选。</p>
            <div class="iframe-container">
                <iframe src="relationship-graph.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">交互图谱</span>
                <span class="feature-tag">关系类型</span>
                <span class="feature-tag">可视化</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">时间线</h3>
                <span class="ui-item-badge">管理</span>
            </div>
            <p class="ui-item-description">展示小说的时间线和重要事件，支持事件分级、角色关联和时间轴可视化。</p>
            <div class="iframe-container">
                <iframe src="timeline.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">时间轴</span>
                <span class="feature-tag">事件分级</span>
                <span class="feature-tag">角色关联</span>
            </div>
        </div>
        
        <div class="ui-item">
            <div class="ui-item-header">
                <h3 class="ui-item-title">个人设置</h3>
                <span class="ui-item-badge">配置</span>
            </div>
            <p class="ui-item-description">用户个人信息和应用设置管理，包含账户设置、应用配置和数据管理功能。</p>
            <div class="iframe-container">
                <iframe src="settings.html" class="ui-iframe"></iframe>
            </div>
            <div class="ui-features">
                <span class="feature-tag">账户管理</span>
                <span class="feature-tag">应用设置</span>
                <span class="feature-tag">数据管理</span>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p class="footer-text">小说人物关系管理器 - 让创作更加高效有序</p>
        <p class="footer-copyright">© 2024 Novel Relationship Manager. All rights reserved.</p>
    </div>
</body>
</html>
