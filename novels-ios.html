<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的小说 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: #000;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        *::-webkit-scrollbar {
            display: none;
        }
        
        /* iPhone 15 Pro 模拟器 */
        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #1c1c1e;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 0 0 8px #2c2c2e, 0 0 0 9px #000, 0 20px 60px rgba(0,0,0,0.8);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* iOS 状态栏 */
        .status-bar {
            height: 54px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            color: #1c1c1e;
            font-size: 15px;
            font-weight: 600;
        }
        
        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #1c1c1e;
            border-radius: 2px;
            position: relative;
        }
        
        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #1c1c1e;
            border-radius: 0 1px 1px 0;
        }
        
        .battery-fill {
            width: 80%;
            height: 100%;
            background: #1c1c1e;
            border-radius: 1px;
        }
        
        /* 导航栏 */
        .nav-bar {
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
            padding: 0 20px 16px;
            border-bottom: 0.5px solid rgba(0,0,0,0.1);
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .nav-title {
            font-size: 34px;
            font-weight: 700;
            color: #1c1c1e;
        }
        
        .add-btn {
            width: 32px;
            height: 32px;
            background: #007AFF;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-bar {
            background: rgba(118,118,128,0.12);
            border-radius: 10px;
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: none;
            font-size: 17px;
            color: #1c1c1e;
            outline: none;
        }
        
        .search-input::placeholder {
            color: #8e8e93;
        }
        
        /* 主要内容 */
        .content {
            flex: 1;
            overflow-y: auto;
            padding: 0 20px;
        }
        
        /* 筛选标签 */
        .filter-section {
            padding: 16px 0;
        }
        
        .filter-scroll {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-tag {
            padding: 8px 16px;
            background: rgba(118,118,128,0.12);
            border-radius: 20px;
            font-size: 15px;
            color: #1c1c1e;
            white-space: nowrap;
            border: none;
            cursor: pointer;
        }
        
        .filter-tag.active {
            background: #007AFF;
            color: white;
        }
        
        /* 小说网格 */
        .novels-grid {
            display: grid;
            gap: 16px;
            padding-bottom: 100px;
        }
        
        .novel-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .novel-cover {
            height: 120px;
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }
        
        .novel-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .novel-status {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 4px 8px;
            background: rgba(0,0,0,0.6);
            border-radius: 8px;
            font-size: 12px;
            color: white;
            font-weight: 500;
        }
        
        .novel-info {
            padding: 16px;
        }
        
        .novel-title {
            font-size: 17px;
            font-weight: 600;
            color: #1c1c1e;
            margin-bottom: 4px;
        }
        
        .novel-description {
            font-size: 15px;
            color: #8e8e93;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .novel-stats {
            display: flex;
            justify-content: space-between;
            font-size: 13px;
            color: #8e8e93;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        /* iOS Tab Bar */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            transition: color 0.2s ease;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Home Indicator */
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #1c1c1e;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="iphone-frame">
        <div class="screen">
            <!-- iOS 状态栏 -->
            <div class="status-bar">
                <div>9:41</div>
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span>100%</span>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-header">
                    <h1 class="nav-title">我的小说</h1>
                    <button class="add-btn">+</button>
                </div>
                <div class="search-bar">
                    <span style="color: #8e8e93;">🔍</span>
                    <input type="text" class="search-input" placeholder="搜索小说...">
                </div>
            </div>
            
            <!-- 主要内容 -->
            <div class="content">
                <!-- 筛选标签 -->
                <div class="filter-section">
                    <div class="filter-scroll">
                        <button class="filter-tag active">全部</button>
                        <button class="filter-tag">进行中</button>
                        <button class="filter-tag">已完成</button>
                        <button class="filter-tag">暂停</button>
                        <button class="filter-tag">玄幻</button>
                        <button class="filter-tag">都市</button>
                        <button class="filter-tag">科幻</button>
                    </div>
                </div>
                
                <!-- 小说网格 -->
                <div class="novels-grid">
                    <div class="novel-card">
                        <div class="novel-cover">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=120&fit=crop&crop=center" alt="修仙传说">
                            <div class="novel-status">进行中</div>
                        </div>
                        <div class="novel-info">
                            <div class="novel-title">修仙传说</div>
                            <div class="novel-description">一个普通少年的修仙之路，充满了挑战与机遇，在这个充满神秘力量的世界中...</div>
                            <div class="novel-stats">
                                <div class="stat-item">
                                    <span>👥</span>
                                    <span>8个角色</span>
                                </div>
                                <div class="stat-item">
                                    <span>🔗</span>
                                    <span>15个关系</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="novel-card">
                        <div class="novel-cover">
                            <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=400&h=120&fit=crop&crop=center" alt="都市风云">
                            <div class="novel-status">已完成</div>
                        </div>
                        <div class="novel-info">
                            <div class="novel-title">都市风云</div>
                            <div class="novel-description">商业精英在都市中的爱恨情仇，权力与金钱的较量，人性的复杂与多面...</div>
                            <div class="novel-stats">
                                <div class="stat-item">
                                    <span>👥</span>
                                    <span>12个角色</span>
                                </div>
                                <div class="stat-item">
                                    <span>🔗</span>
                                    <span>28个关系</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="novel-card">
                        <div class="novel-cover">
                            <img src="https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400&h=120&fit=crop&crop=center" alt="星际征途">
                            <div class="novel-status">进行中</div>
                        </div>
                        <div class="novel-info">
                            <div class="novel-title">星际征途</div>
                            <div class="novel-description">人类踏出地球，在浩瀚宇宙中寻找新的家园，面对未知的挑战和机遇...</div>
                            <div class="novel-stats">
                                <div class="stat-item">
                                    <span>👥</span>
                                    <span>6个角色</span>
                                </div>
                                <div class="stat-item">
                                    <span>🔗</span>
                                    <span>10个关系</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- iOS Tab Bar -->
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <div class="tab-icon">🏠</div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="#" class="tab-item active">
                    <div class="tab-icon">📚</div>
                    <div class="tab-label">小说</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">👥</div>
                    <div class="tab-label">角色</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">⚙️</div>
                    <div class="tab-label">设置</div>
                </a>
            </div>
            
            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
