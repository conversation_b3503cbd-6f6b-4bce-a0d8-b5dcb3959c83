<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关系图谱 - 修仙传说</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: #000;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        *::-webkit-scrollbar {
            display: none;
        }
        
        /* iPhone 15 Pro 模拟器 */
        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #1c1c1e;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 0 0 8px #2c2c2e, 0 0 0 9px #000, 0 20px 60px rgba(0,0,0,0.8);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #1c1c1e;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* iOS 状态栏 */
        .status-bar {
            height: 54px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            color: white;
            font-size: 15px;
            font-weight: 600;
        }
        
        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid white;
            border-radius: 2px;
            position: relative;
        }
        
        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: white;
            border-radius: 0 1px 1px 0;
        }
        
        .battery-fill {
            width: 80%;
            height: 100%;
            background: white;
            border-radius: 1px;
        }
        
        /* 导航栏 */
        .nav-bar {
            background: rgba(28,28,30,0.8);
            backdrop-filter: blur(20px);
            padding: 0 20px 16px;
            border-bottom: 0.5px solid rgba(255,255,255,0.1);
        }
        
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #007AFF;
            font-size: 17px;
            cursor: pointer;
        }
        
        .nav-title {
            font-size: 17px;
            font-weight: 600;
            color: white;
        }
        
        .nav-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: rgba(255,255,255,0.1);
            border: none;
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 13px;
            color: white;
            cursor: pointer;
        }
        
        .action-btn.primary {
            background: #007AFF;
        }
        
        /* 图谱容器 */
        .graph-container {
            flex: 1;
            position: relative;
            background: radial-gradient(circle at 50% 50%, #2c2c2e 0%, #1c1c1e 100%);
            overflow: hidden;
        }
        
        .graph-canvas {
            width: 100%;
            height: 100%;
            position: relative;
        }
        
        /* 连接线 */
        .connection {
            position: absolute;
            height: 2px;
            transform-origin: left center;
            z-index: 1;
        }
        
        .connection.friend {
            background: linear-gradient(90deg, #34C759, #30D158);
        }
        
        .connection.enemy {
            background: linear-gradient(90deg, #FF3B30, #FF6482);
        }
        
        .connection.love {
            background: linear-gradient(90deg, #FF2D92, #FF6AC1);
        }
        
        .connection.mentor {
            background: linear-gradient(90deg, #FF9500, #FFCC02);
        }
        
        /* 角色节点 */
        .node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 5;
            border: 3px solid rgba(255,255,255,0.2);
        }
        
        .node:active {
            transform: scale(1.1);
        }
        
        .node.main {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            width: 80px;
            height: 80px;
            font-size: 16px;
        }
        
        .node.supporting {
            background: linear-gradient(135deg, #34C759 0%, #30D158 100%);
        }
        
        .node.villain {
            background: linear-gradient(135deg, #FF3B30 0%, #FF6482 100%);
        }
        
        .node.minor {
            background: linear-gradient(135deg, #8E8E93 0%, #AEAEB2 100%);
            width: 50px;
            height: 50px;
            font-size: 12px;
        }
        
        .node-label {
            position: absolute;
            bottom: -24px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: rgba(255,255,255,0.8);
            white-space: nowrap;
            background: rgba(0,0,0,0.6);
            padding: 2px 6px;
            border-radius: 4px;
            backdrop-filter: blur(10px);
        }
        
        /* 图例 */
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(28,28,30,0.8);
            border-radius: 12px;
            padding: 12px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        
        .legend-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
            font-size: 12px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .legend-color {
            width: 16px;
            height: 2px;
            border-radius: 1px;
        }
        
        .legend-text {
            font-size: 11px;
            color: rgba(255,255,255,0.8);
        }
        
        /* 控制按钮 */
        .controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            width: 36px;
            height: 36px;
            background: rgba(28,28,30,0.8);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            color: white;
            backdrop-filter: blur(20px);
        }
        
        .control-btn:active {
            transform: scale(0.95);
        }
        
        /* iOS Tab Bar */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: rgba(28,28,30,0.8);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            transition: color 0.2s ease;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Home Indicator */
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="iphone-frame">
        <div class="screen">
            <!-- iOS 状态栏 -->
            <div class="status-bar">
                <div>9:41</div>
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span>100%</span>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <div class="nav-header">
                    <button class="back-btn">← 返回</button>
                    <h1 class="nav-title">关系图谱</h1>
                    <div class="nav-actions">
                        <button class="action-btn">筛选</button>
                        <button class="action-btn primary">编辑</button>
                    </div>
                </div>
            </div>
            
            <!-- 图谱容器 -->
            <div class="graph-container">
                <div class="graph-canvas">
                    <!-- 连接线 -->
                    <div class="connection friend" style="left: 200px; top: 200px; width: 120px; transform: rotate(30deg);"></div>
                    <div class="connection love" style="left: 200px; top: 200px; width: 100px; transform: rotate(-45deg);"></div>
                    <div class="connection enemy" style="left: 320px; top: 230px; width: 80px; transform: rotate(60deg);"></div>
                    <div class="connection mentor" style="left: 150px; top: 300px; width: 90px; transform: rotate(-30deg);"></div>
                    
                    <!-- 角色节点 -->
                    <div class="node main" style="left: 170px; top: 170px;">
                        李
                        <div class="node-label">李逍遥</div>
                    </div>
                    
                    <div class="node supporting" style="left: 290px; top: 200px;">
                        赵
                        <div class="node-label">赵灵儿</div>
                    </div>
                    
                    <div class="node supporting" style="left: 270px; top: 120px;">
                        林
                        <div class="node-label">林月如</div>
                    </div>
                    
                    <div class="node villain" style="left: 380px; top: 280px;">
                        王
                        <div class="node-label">王魔头</div>
                    </div>
                    
                    <div class="node supporting" style="left: 120px; top: 270px;">
                        张
                        <div class="node-label">张真人</div>
                    </div>
                    
                    <div class="node minor" style="left: 320px; top: 350px;">
                        小
                        <div class="node-label">小二</div>
                    </div>
                </div>
                
                <!-- 图例 -->
                <div class="legend">
                    <div class="legend-title">关系类型</div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(90deg, #34C759, #30D158);"></div>
                        <span class="legend-text">朋友</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(90deg, #FF2D92, #FF6AC1);"></div>
                        <span class="legend-text">恋人</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(90deg, #FF3B30, #FF6482);"></div>
                        <span class="legend-text">敌人</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(90deg, #FF9500, #FFCC02);"></div>
                        <span class="legend-text">师徒</span>
                    </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class="controls">
                    <button class="control-btn" title="搜索">🔍</button>
                    <button class="control-btn" title="缩放适应">⌂</button>
                    <button class="control-btn" title="放大">+</button>
                    <button class="control-btn" title="缩小">-</button>
                </div>
            </div>
            
            <!-- iOS Tab Bar -->
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <div class="tab-icon">🏠</div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">📚</div>
                    <div class="tab-label">小说</div>
                </a>
                <a href="#" class="tab-item active">
                    <div class="tab-icon">👥</div>
                    <div class="tab-label">角色</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">⚙️</div>
                    <div class="tab-label">设置</div>
                </a>
            </div>
            
            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
