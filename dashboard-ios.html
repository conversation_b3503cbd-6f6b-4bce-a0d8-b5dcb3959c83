<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制面板 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', sans-serif;
            background: #000;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        *::-webkit-scrollbar {
            display: none;
        }
        
        /* iPhone 15 Pro 模拟器 */
        .iphone-frame {
            width: 393px;
            height: 852px;
            background: #1c1c1e;
            border-radius: 47px;
            padding: 2px;
            box-shadow: 0 0 0 8px #2c2c2e, 0 0 0 9px #000, 0 20px 60px rgba(0,0,0,0.8);
            position: relative;
        }
        
        .screen {
            width: 100%;
            height: 100%;
            background: #f2f2f7;
            border-radius: 45px;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        /* iOS 状态栏 */
        .status-bar {
            height: 54px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 24px;
            color: #1c1c1e;
            font-size: 15px;
            font-weight: 600;
        }
        
        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #1c1c1e;
            border-radius: 2px;
            position: relative;
        }
        
        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #1c1c1e;
            border-radius: 0 1px 1px 0;
        }
        
        .battery-fill {
            width: 80%;
            height: 100%;
            background: #1c1c1e;
            border-radius: 1px;
        }
        
        /* 导航栏 */
        .nav-bar {
            padding: 0 20px 16px;
        }
        
        .nav-title {
            font-size: 34px;
            font-weight: 700;
            color: #1c1c1e;
            margin-bottom: 2px;
        }
        
        .nav-subtitle {
            font-size: 17px;
            color: #8e8e93;
        }
        
        /* 主要内容 */
        .content {
            flex: 1;
            padding: 0 20px;
            overflow-y: auto;
        }
        
        /* 统计卡片 */
        .stats-section {
            margin-bottom: 24px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .stat-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            font-size: 16px;
        }
        
        .stat-icon.novels {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
        }
        
        .stat-icon.characters {
            background: linear-gradient(135deg, #34C759, #30D158);
            color: white;
        }
        
        .stat-icon.relationships {
            background: linear-gradient(135deg, #FF3B30, #FF6482);
            color: white;
        }
        
        .stat-icon.notes {
            background: linear-gradient(135deg, #FF9500, #FFCC02);
            color: white;
        }
        
        .stat-number {
            font-size: 28px;
            font-weight: 700;
            color: #1c1c1e;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 15px;
            color: #8e8e93;
        }
        
        /* 快速操作 */
        .quick-actions {
            margin-bottom: 24px;
        }
        
        .section-title {
            font-size: 22px;
            font-weight: 600;
            color: #1c1c1e;
            margin-bottom: 16px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .action-btn {
            background: white;
            border: none;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .action-btn:active {
            transform: scale(0.95);
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
        }
        
        .action-icon.create {
            background: linear-gradient(135deg, #007AFF, #5856D6);
        }
        
        .action-icon.add-character {
            background: linear-gradient(135deg, #34C759, #30D158);
        }
        
        .action-icon.relationship {
            background: linear-gradient(135deg, #FF3B30, #FF6482);
        }
        
        .action-icon.note {
            background: linear-gradient(135deg, #FF9500, #FFCC02);
        }
        
        .action-text {
            font-size: 15px;
            font-weight: 500;
            color: #1c1c1e;
        }
        
        /* 最近活动 */
        .recent-activity {
            margin-bottom: 100px;
        }
        
        .activity-list {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            padding: 16px 20px;
            border-bottom: 0.5px solid #f2f2f7;
            display: flex;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            background: #f2f2f7;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            font-size: 15px;
            color: #1c1c1e;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 13px;
            color: #8e8e93;
        }
        
        /* iOS Tab Bar */
        .tab-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 83px;
            background: rgba(255,255,255,0.8);
            backdrop-filter: blur(20px);
            border-top: 0.5px solid rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #8e8e93;
            transition: color 0.2s ease;
        }
        
        .tab-item.active {
            color: #007AFF;
        }
        
        .tab-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }
        
        .tab-label {
            font-size: 10px;
            font-weight: 500;
        }
        
        /* Home Indicator */
        .home-indicator {
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 134px;
            height: 5px;
            background: #1c1c1e;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="iphone-frame">
        <div class="screen">
            <!-- iOS 状态栏 -->
            <div class="status-bar">
                <div>9:41</div>
                <div style="display: flex; align-items: center; gap: 6px;">
                    <span>100%</span>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>
            
            <!-- 导航栏 -->
            <div class="nav-bar">
                <h1 class="nav-title">欢迎回来</h1>
                <p class="nav-subtitle">继续你的创作之旅</p>
            </div>
            
            <!-- 主要内容 -->
            <div class="content">
                <!-- 统计数据 -->
                <div class="stats-section">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon novels">📚</div>
                            <div class="stat-number">5</div>
                            <div class="stat-label">小说作品</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon characters">👥</div>
                            <div class="stat-number">23</div>
                            <div class="stat-label">角色数量</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon relationships">🔗</div>
                            <div class="stat-number">45</div>
                            <div class="stat-label">关系连接</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon notes">📝</div>
                            <div class="stat-number">12</div>
                            <div class="stat-label">笔记数量</div>
                        </div>
                    </div>
                </div>
                
                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h2 class="section-title">快速操作</h2>
                    <div class="action-grid">
                        <button class="action-btn">
                            <div class="action-icon create">➕</div>
                            <div class="action-text">创建小说</div>
                        </button>
                        <button class="action-btn">
                            <div class="action-icon add-character">👤</div>
                            <div class="action-text">添加角色</div>
                        </button>
                        <button class="action-btn">
                            <div class="action-icon relationship">🔗</div>
                            <div class="action-text">建立关系</div>
                        </button>
                        <button class="action-btn">
                            <div class="action-icon note">📝</div>
                            <div class="action-text">写笔记</div>
                        </button>
                    </div>
                </div>
                
                <!-- 最近活动 -->
                <div class="recent-activity">
                    <h2 class="section-title">最近活动</h2>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">👤</div>
                            <div class="activity-content">
                                <div class="activity-text">添加了角色"李小明"</div>
                                <div class="activity-time">2小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">🔗</div>
                            <div class="activity-content">
                                <div class="activity-text">建立了"张三"和"李四"的朋友关系</div>
                                <div class="activity-time">5小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📚</div>
                            <div class="activity-content">
                                <div class="activity-text">创建了新小说"都市传说"</div>
                                <div class="activity-time">1天前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- iOS Tab Bar -->
            <div class="tab-bar">
                <a href="#" class="tab-item active">
                    <div class="tab-icon">🏠</div>
                    <div class="tab-label">首页</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">📚</div>
                    <div class="tab-label">小说</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">👥</div>
                    <div class="tab-label">角色</div>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">⚙️</div>
                    <div class="tab-label">设置</div>
                </a>
            </div>
            
            <!-- Home Indicator -->
            <div class="home-indicator"></div>
        </div>
    </div>
</body>
</html>
