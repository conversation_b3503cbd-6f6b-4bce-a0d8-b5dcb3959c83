<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        *::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #667eea;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .profile-section {
            background: white;
            margin: 20px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            position: relative;
        }
        
        .avatar-edit {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            cursor: pointer;
        }
        
        .profile-name {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .profile-email {
            font-size: 14px;
            color: #666;
            margin-bottom: 16px;
        }
        
        .profile-stats {
            display: flex;
            justify-content: space-around;
            padding-top: 16px;
            border-top: 1px solid #f1f5f9;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: 700;
            color: #333;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }
        
        .settings-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .setting-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-item:hover {
            background: #f8fafc;
        }
        
        .setting-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .setting-icon {
            width: 32px;
            height: 32px;
            background: #f1f5f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .setting-info {
            flex: 1;
        }
        
        .setting-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .setting-description {
            font-size: 12px;
            color: #666;
        }
        
        .setting-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .setting-value {
            font-size: 14px;
            color: #666;
        }
        
        .setting-arrow {
            font-size: 16px;
            color: #999;
        }
        
        .toggle-switch {
            width: 44px;
            height: 24px;
            background: #e1e5e9;
            border-radius: 12px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .toggle-knob {
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .toggle-switch.active .toggle-knob {
            transform: translateX(20px);
        }
        
        .danger-section {
            background: white;
            margin: 0 20px 20px;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #fee2e2;
        }
        
        .danger-section .section-header {
            color: #dc2626;
            background: #fef2f2;
        }
        
        .danger-item {
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .danger-item:hover {
            background: #fef2f2;
        }
        
        .danger-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .danger-icon {
            width: 32px;
            height: 32px;
            background: #fee2e2;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #dc2626;
        }
        
        .danger-title {
            font-size: 14px;
            font-weight: 500;
            color: #dc2626;
        }
        
        .version-info {
            text-align: center;
            padding: 20px;
            color: #999;
            font-size: 12px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <button class="back-btn">←</button>
                <h1 class="title">设置</h1>
            </div>
        </div>
    </div>
    
    <div class="profile-section">
        <div class="profile-avatar">
            👤
            <div class="avatar-edit">✏️</div>
        </div>
        <div class="profile-name">张三</div>
        <div class="profile-email"><EMAIL></div>
        <div class="profile-stats">
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">小说</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">23</div>
                <div class="stat-label">角色</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">45</div>
                <div class="stat-label">关系</div>
            </div>
        </div>
    </div>
    
    <div class="settings-section">
        <div class="section-header">账户设置</div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">👤</div>
                <div class="setting-info">
                    <div class="setting-title">个人信息</div>
                    <div class="setting-description">编辑用户名、邮箱等信息</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">🔒</div>
                <div class="setting-info">
                    <div class="setting-title">修改密码</div>
                    <div class="setting-description">更改登录密码</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
    </div>
    
    <div class="settings-section">
        <div class="section-header">应用设置</div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">🌙</div>
                <div class="setting-info">
                    <div class="setting-title">深色模式</div>
                    <div class="setting-description">切换应用主题</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="toggle-switch">
                    <div class="toggle-knob"></div>
                </div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">🔔</div>
                <div class="setting-info">
                    <div class="setting-title">推送通知</div>
                    <div class="setting-description">接收应用通知</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="toggle-switch active">
                    <div class="toggle-knob"></div>
                </div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">🌐</div>
                <div class="setting-info">
                    <div class="setting-title">语言</div>
                    <div class="setting-description">选择应用语言</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-value">简体中文</div>
                <div class="setting-arrow">›</div>
            </div>
        </div>
    </div>
    
    <div class="settings-section">
        <div class="section-header">数据管理</div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">📤</div>
                <div class="setting-info">
                    <div class="setting-title">导出数据</div>
                    <div class="setting-description">导出所有小说和角色数据</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">📥</div>
                <div class="setting-info">
                    <div class="setting-title">导入数据</div>
                    <div class="setting-description">从文件导入数据</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">☁️</div>
                <div class="setting-info">
                    <div class="setting-title">云同步</div>
                    <div class="setting-description">自动备份到云端</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="toggle-switch active">
                    <div class="toggle-knob"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="settings-section">
        <div class="section-header">帮助与支持</div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">❓</div>
                <div class="setting-info">
                    <div class="setting-title">使用帮助</div>
                    <div class="setting-description">查看使用教程</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">📧</div>
                <div class="setting-info">
                    <div class="setting-title">联系我们</div>
                    <div class="setting-description">反馈问题或建议</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
        <div class="setting-item">
            <div class="setting-left">
                <div class="setting-icon">ℹ️</div>
                <div class="setting-info">
                    <div class="setting-title">关于应用</div>
                    <div class="setting-description">版本信息和开发者</div>
                </div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
    </div>
    
    <div class="danger-section">
        <div class="section-header">危险操作</div>
        <div class="danger-item">
            <div class="danger-left">
                <div class="danger-icon">🗑️</div>
                <div class="danger-title">删除所有数据</div>
            </div>
            <div class="setting-right">
                <div class="setting-arrow">›</div>
            </div>
        </div>
    </div>
    
    <div class="version-info">
        小说人物关系管理器 v1.0.0<br>
        © 2024 Novel Relationship Manager
    </div>
    
    <div style="height: 80px;"></div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item active">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
