<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>关系图谱 - 修仙传说</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        *::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e1e5e9;
            position: relative;
            z-index: 10;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #667eea;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: none;
            border: 1px solid #e1e5e9;
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            color: #666;
        }
        
        .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .graph-container {
            position: relative;
            height: calc(100vh - 140px);
            background: white;
            overflow: hidden;
        }
        
        .graph-canvas {
            width: 100%;
            height: 100%;
            position: relative;
            background: radial-gradient(circle at 50% 50%, #f8fafc 0%, #e2e8f0 100%);
        }
        
        .node {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 5;
        }
        
        .node:hover {
            transform: scale(1.1);
            z-index: 10;
        }
        
        .node.main {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 80px;
            height: 80px;
            font-size: 16px;
        }
        
        .node.supporting {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
        }
        
        .node.villain {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .node.minor {
            background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
            width: 50px;
            height: 50px;
            font-size: 12px;
        }
        
        .connection {
            position: absolute;
            height: 2px;
            background: #cbd5e1;
            transform-origin: left center;
            z-index: 1;
        }
        
        .connection.friend {
            background: linear-gradient(90deg, #22c55e, #4ade80);
        }
        
        .connection.enemy {
            background: linear-gradient(90deg, #ef4444, #f87171);
        }
        
        .connection.love {
            background: linear-gradient(90deg, #ec4899, #f472b6);
        }
        
        .connection.family {
            background: linear-gradient(90deg, #3b82f6, #60a5fa);
        }
        
        .connection.mentor {
            background: linear-gradient(90deg, #f59e0b, #fbbf24);
        }
        
        .node-label {
            position: absolute;
            bottom: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 10px;
            color: #666;
            white-space: nowrap;
            background: rgba(255,255,255,0.9);
            padding: 2px 6px;
            border-radius: 4px;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            padding: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 12px;
        }
        
        .legend-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
        }
        
        .legend-color {
            width: 16px;
            height: 2px;
            border-radius: 1px;
        }
        
        .controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            display: flex;
            gap: 8px;
        }
        
        .control-btn {
            width: 36px;
            height: 36px;
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 16px;
            color: #666;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .control-btn:hover {
            background: #f8fafc;
        }
        
        .search-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            background: white;
            border-radius: 8px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: none;
        }
        
        .search-input {
            border: none;
            outline: none;
            padding: 4px 8px;
            font-size: 14px;
            width: 200px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <button class="back-btn">←</button>
                <h1 class="title">关系图谱</h1>
            </div>
            <div class="header-actions">
                <button class="action-btn">筛选</button>
                <button class="action-btn">导出</button>
                <button class="action-btn primary">编辑</button>
            </div>
        </div>
    </div>
    
    <div class="graph-container">
        <div class="graph-canvas">
            <!-- 连接线 -->
            <div class="connection friend" style="left: 200px; top: 200px; width: 120px; transform: rotate(30deg);"></div>
            <div class="connection love" style="left: 200px; top: 200px; width: 100px; transform: rotate(-45deg);"></div>
            <div class="connection enemy" style="left: 320px; top: 230px; width: 80px; transform: rotate(60deg);"></div>
            <div class="connection mentor" style="left: 150px; top: 300px; width: 90px; transform: rotate(-30deg);"></div>
            <div class="connection family" style="left: 240px; top: 350px; width: 110px; transform: rotate(15deg);"></div>
            
            <!-- 角色节点 -->
            <div class="node main" style="left: 170px; top: 170px;">
                李
                <div class="node-label">李逍遥</div>
            </div>
            
            <div class="node supporting" style="left: 290px; top: 200px;">
                赵
                <div class="node-label">赵灵儿</div>
            </div>
            
            <div class="node supporting" style="left: 270px; top: 120px;">
                林
                <div class="node-label">林月如</div>
            </div>
            
            <div class="node villain" style="left: 380px; top: 280px;">
                王
                <div class="node-label">王魔头</div>
            </div>
            
            <div class="node supporting" style="left: 120px; top: 270px;">
                张
                <div class="node-label">张真人</div>
            </div>
            
            <div class="node minor" style="left: 320px; top: 350px;">
                小
                <div class="node-label">小二</div>
            </div>
            
            <div class="node supporting" style="left: 80px; top: 180px;">
                老
                <div class="node-label">老板娘</div>
            </div>
            
            <div class="node minor" style="left: 350px; top: 80px;">
                阿
                <div class="node-label">阿奴</div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-title">关系类型</div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #22c55e, #4ade80);"></div>
                <span>朋友</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #ec4899, #f472b6);"></div>
                <span>恋人</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #ef4444, #f87171);"></div>
                <span>敌人</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #f59e0b, #fbbf24);"></div>
                <span>师徒</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: linear-gradient(90deg, #3b82f6, #60a5fa);"></div>
                <span>亲情</span>
            </div>
        </div>
        
        <div class="search-overlay">
            <input type="text" class="search-input" placeholder="搜索角色...">
        </div>
        
        <div class="controls">
            <button class="control-btn" title="搜索">🔍</button>
            <button class="control-btn" title="缩放适应">⌂</button>
            <button class="control-btn" title="放大">+</button>
            <button class="control-btn" title="缩小">-</button>
            <button class="control-btn" title="重置">↻</button>
        </div>
    </div>
    
    <div style="height: 60px;"></div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
