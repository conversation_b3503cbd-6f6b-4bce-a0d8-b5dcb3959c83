# 小说人物关系管理器 - 产品设计文档

## 📋 产品概述

### 产品名称
小说人物关系管理器 (Novel Relationship Manager)

### 产品定位
专门用于管理小说中人物关系的移动端Web应用程序，主要面向作家、编剧或小说爱好者。

### 目标用户
- **小说作家**：管理复杂的人物关系网络
- **编剧**：跟踪剧本中的角色发展  
- **游戏策划**：设计游戏世界观和角色关系
- **文学爱好者**：分析经典作品的人物关系

### 核心价值
帮助用户可视化管理小说中复杂的人物关系，提供直观的关系图谱和时间线管理，提升创作效率。

## 🎯 核心功能模块

### 1. 用户管理系统
- 用户注册、登录、认证
- 基于Session的会话管理
- 用户权限控制（管理员/普通用户）
- 个人设置管理

### 2. 小说管理
- **创建小说**：支持手动创建或从外部书籍API导入
- **小说信息**：标题、描述、封面图片、类型、状态等
- **小说分类**：自定义小说类型和分类管理
- **小说状态**：进行中、已完成等状态跟踪
- **搜索和过滤**：按类型、标题、创建时间等多维度筛选

### 3. 角色管理
- **角色创建**：为每部小说添加角色
- **角色信息**：姓名、描述、头像、所属小说等
- **角色展示**：卡片式布局，支持头像上传
- **角色编辑**：完整的CRUD操作

### 4. 关系网络管理 ⭐ (核心功能)
- **关系类型定义**：自定义关系类型（朋友、敌人、恋人、同事等）
- **颜色编码**：为不同关系类型设置颜色标识
- **关系创建**：在角色之间建立各种类型的关系
- **可视化图谱**：使用D3.js实现的交互式关系网络图
- **图谱功能**：
  - 节点拖拽和缩放
  - 角色选择和高亮
  - 关系线条的颜色和标签显示
  - 响应式布局适配

### 5. 时间线管理
- **事件创建**：为小说创建时间线事件
- **事件属性**：标题、描述、日期、重要程度、关联角色
- **重要程度分级**：minor、normal、important、critical
- **可视化时间线**：以时间轴形式展示剧情发展

### 6. 笔记系统
- **灵感记录**：创建与小说相关的笔记
- **角色关联**：笔记可以关联特定角色
- **标签系统**：支持给笔记添加标签分类
- **富文本编辑**：支持格式化的笔记内容

### 7. 数据导入功能
- **书籍API集成**：从外部图书数据库导入书籍信息
- **自动填充**：利用外部数据自动创建小说基本信息
- **数据映射**：将外部数据映射到内部数据结构

## 🎨 设计特色

### 响应式设计
- **桌面端**：侧边栏 + 主内容区布局
- **移动端**：底部标签栏导航
- **自适应**：根据屏幕尺寸自动调整布局

### 现代化UI组件
- 基于Radix UI和Tailwind CSS
- 卡片式设计语言
- 丰富的交互动画和过渡效果
- 支持深色/浅色主题切换

### 可视化元素
- **关系图谱**：使用SVG和Canvas的交互式网络图
- **瀑布流布局**：小说展示采用Pinterest风格布局
- **进度指示器**：实时显示加载状态
- **图标系统**：使用Lucide React图标库

## 📱 移动端优化

### 触摸友好
- 大尺寸点击区域
- 滑动手势支持
- 触摸反馈动画

### 性能优化
- 图片懒加载
- 组件级代码分割
- 缓存策略优化

### 离线支持
- Service Worker集成
- 离线状态提示
- 数据同步机制

## 🎮 用户交互流程

### 新用户onboarding
注册 → 创建第一部小说 → 添加角色 → 建立关系

### 日常使用
查看控制面板 → 管理角色/关系 → 查看可视化图谱

### 内容创作
添加时间线事件 → 记录笔记 → 跟踪剧情发展

## 🔧 技术架构

### 前端技术栈
- React 18 + TypeScript：主框架
- Wouter：轻量级路由管理
- Tanstack Query：数据获取和缓存
- Zustand：状态管理
- Framer Motion：动画效果

### UI框架和工具
- Tailwind CSS：样式框架
- Radix UI：无样式UI组件库
- D3.js：数据可视化
- React Hook Form：表单管理
- Zod：数据验证

### 开发体验
- Vite：快速构建工具
- PWA支持：可安装的Web应用
- 热重载：开发时实时更新
- TypeScript：完整的类型安全

## 📱 页面架构设计

### 登录页面
**用途**：用户身份验证和应用入口
**核心功能**：用户登录表单、注册链接跳转、忘记密码功能、第三方登录选项

### 注册页面
**用途**：新用户账户创建
**核心功能**：注册表单、用户协议确认、验证码验证、返回登录页面链接

### 首页/控制面板
**用途**：应用主入口，展示用户数据概览
**核心功能**：用户小说列表概览、最近活动动态、快速操作入口、统计数据展示

### 小说列表页面
**用途**：展示和管理用户的所有小说
**核心功能**：小说卡片展示、搜索和筛选功能、排序选项、创建新小说按钮

### 小说详情页面
**用途**：单个小说的详细信息展示和管理
**核心功能**：小说基本信息展示、角色列表预览、关系图谱入口、时间线入口

### 角色管理页面
**用途**：管理特定小说的所有角色
**核心功能**：角色卡片网格展示、添加新角色按钮、角色搜索和筛选

### 角色详情页面
**用途**：单个角色的详细信息管理
**核心功能**：角色基本信息、角色关系列表、相关时间线事件、相关笔记

### 关系图谱页面
**用途**：可视化展示角色关系网络
**核心功能**：交互式关系网络图、节点拖拽和缩放、关系类型筛选、图谱导出

### 关系编辑页面
**用途**：创建和编辑角色之间的关系
**核心功能**：选择关系角色、设置关系类型、关系描述输入、关系强度设置

### 时间线页面
**用途**：展示小说的时间线和重要事件
**核心功能**：时间轴可视化展示、事件卡片展示、事件重要程度标识、添加新事件

### 事件详情页面
**用途**：单个时间线事件的详细管理
**核心功能**：事件基本信息、重要程度设置、关联角色选择、事件标签管理

### 笔记列表页面
**用途**：展示和管理与小说相关的所有笔记
**核心功能**：笔记卡片展示、笔记搜索和筛选、标签筛选、创建新笔记

### 笔记详情页面
**用途**：单个笔记的详细编辑和查看
**核心功能**：富文本编辑器、笔记标题和内容、标签管理、关联角色选择

### 个人设置页面
**用途**：用户个人信息和应用设置管理
**核心功能**：个人信息编辑、密码修改、主题设置、通知设置、数据导出/导入

### 关系类型管理页面
**用途**：自定义和管理关系类型
**核心功能**：关系类型列表、添加新关系类型、关系类型颜色设置、编辑和删除
