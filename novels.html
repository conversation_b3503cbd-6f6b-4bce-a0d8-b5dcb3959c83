<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的小说 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        *::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .add-btn {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-bar {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: #f8fafc;
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .filters {
            padding: 16px 20px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-tab {
            padding: 8px 16px;
            background: #f1f5f9;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .novels-grid {
            padding: 20px;
            display: grid;
            gap: 16px;
        }
        
        .novel-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .novel-card:hover {
            transform: translateY(-2px);
        }
        
        .novel-cover {
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        
        .novel-status {
            position: absolute;
            top: 8px;
            right: 8px;
            padding: 4px 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .novel-info {
            padding: 16px;
        }
        
        .novel-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .novel-description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .novel-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }
        
        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }
        
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .empty-text {
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .empty-subtext {
            font-size: 14px;
            color: #999;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <h1 class="title">我的小说</h1>
            <button class="add-btn">+</button>
        </div>
        <div class="search-bar">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" placeholder="搜索小说...">
        </div>
    </div>
    
    <div class="filters">
        <div class="filter-tabs">
            <button class="filter-tab active">全部</button>
            <button class="filter-tab">进行中</button>
            <button class="filter-tab">已完成</button>
            <button class="filter-tab">暂停</button>
            <button class="filter-tab">玄幻</button>
            <button class="filter-tab">都市</button>
            <button class="filter-tab">科幻</button>
        </div>
    </div>
    
    <div class="novels-grid">
        <div class="novel-card">
            <div class="novel-cover">
                📖
                <div class="novel-status">进行中</div>
            </div>
            <div class="novel-info">
                <div class="novel-title">修仙传说</div>
                <div class="novel-description">一个普通少年的修仙之路，充满了挑战与机遇，在这个充满神秘力量的世界中...</div>
                <div class="novel-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>8个角色</span>
                    </div>
                    <div class="stat-item">
                        <span>🔗</span>
                        <span>15个关系</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="novel-card">
            <div class="novel-cover">
                🏙️
                <div class="novel-status">已完成</div>
            </div>
            <div class="novel-info">
                <div class="novel-title">都市风云</div>
                <div class="novel-description">商业精英在都市中的爱恨情仇，权力与金钱的较量，人性的复杂与多面...</div>
                <div class="novel-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>12个角色</span>
                    </div>
                    <div class="stat-item">
                        <span>🔗</span>
                        <span>28个关系</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="novel-card">
            <div class="novel-cover">
                🚀
                <div class="novel-status">进行中</div>
            </div>
            <div class="novel-info">
                <div class="novel-title">星际征途</div>
                <div class="novel-description">人类踏出地球，在浩瀚宇宙中寻找新的家园，面对未知的挑战和机遇...</div>
                <div class="novel-stats">
                    <div class="stat-item">
                        <span>👥</span>
                        <span>6个角色</span>
                    </div>
                    <div class="stat-item">
                        <span>🔗</span>
                        <span>10个关系</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="height: 80px;"></div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item active">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
