<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理 - 修仙传说</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #667eea;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
        }
        
        .add-btn {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .search-bar {
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: #f8fafc;
        }
        
        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }
        
        .filters {
            padding: 16px 20px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-tab {
            padding: 8px 16px;
            background: #f1f5f9;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .characters-grid {
            padding: 20px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .character-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            position: relative;
        }
        
        .character-card:hover {
            transform: translateY(-2px);
        }
        
        .character-menu {
            position: absolute;
            top: 12px;
            right: 12px;
            background: none;
            border: none;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }
        
        .character-avatar {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: 600;
        }
        
        .character-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .character-role {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .character-description {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .character-stats {
            display: flex;
            justify-content: space-around;
            padding-top: 12px;
            border-top: 1px solid #f1f5f9;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .stat-label {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }
        
        .role-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .role-main {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .role-supporting {
            background: #e8f5e8;
            color: #388e3c;
        }
        
        .role-minor {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        
        .role-villain {
            background: #ffebee;
            color: #d32f2f;
        }
        
        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s ease;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <button class="back-btn">←</button>
                <h1 class="title">角色管理</h1>
            </div>
            <button class="add-btn">+</button>
        </div>
        <div class="search-bar">
            <span class="search-icon">🔍</span>
            <input type="text" class="search-input" placeholder="搜索角色...">
        </div>
    </div>
    
    <div class="filters">
        <div class="filter-tabs">
            <button class="filter-tab active">全部</button>
            <button class="filter-tab">主角</button>
            <button class="filter-tab">配角</button>
            <button class="filter-tab">反派</button>
            <button class="filter-tab">龙套</button>
        </div>
    </div>
    
    <div class="characters-grid">
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">李</div>
            <div class="role-badge role-main">主角</div>
            <div class="character-name">李逍遥</div>
            <div class="character-role">修仙者</div>
            <div class="character-description">天赋异禀的少年，踏上修仙之路，性格坚毅不屈</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">12</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
        
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">赵</div>
            <div class="role-badge role-main">女主</div>
            <div class="character-name">赵灵儿</div>
            <div class="character-role">仙女</div>
            <div class="character-description">美丽善良的仙女，拥有强大的法力和纯净的心灵</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
        
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">林</div>
            <div class="role-badge role-supporting">配角</div>
            <div class="character-name">林月如</div>
            <div class="character-role">剑客</div>
            <div class="character-description">武功高强的女剑客，性格直爽，是主角的好友</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">4</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">6</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
        
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">王</div>
            <div class="role-badge role-villain">反派</div>
            <div class="character-name">王魔头</div>
            <div class="character-role">魔教教主</div>
            <div class="character-description">邪恶的魔教教主，实力强大，是主角的宿敌</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
        
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">张</div>
            <div class="role-badge role-supporting">师父</div>
            <div class="character-name">张真人</div>
            <div class="character-role">仙人</div>
            <div class="character-description">隐世高人，主角的师父，传授修仙功法</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">4</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
        
        <div class="character-card">
            <button class="character-menu">⋮</button>
            <div class="character-avatar">小</div>
            <div class="role-badge role-minor">龙套</div>
            <div class="character-name">小二</div>
            <div class="character-role">店小二</div>
            <div class="character-description">客栈的店小二，为主角提供信息和帮助</div>
            <div class="character-stats">
                <div class="stat-item">
                    <div class="stat-number">1</div>
                    <div class="stat-label">关系</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">事件</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="height: 80px;"></div>
    
    <button class="fab">+</button>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item active">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
