<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow: hidden;
        }

        /* 强制移除所有滚动条 */
        * {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        *::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }
        
        .register-container {
            background: white;
            border-radius: 16px;
            padding: 32px;
            width: 100%;
            max-width: 320px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .back-btn {
            position: absolute;
            top: 16px;
            left: 16px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #667eea;
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .password-strength {
            margin-top: 8px;
            font-size: 12px;
        }
        
        .strength-bar {
            height: 4px;
            background: #e1e5e9;
            border-radius: 2px;
            margin: 4px 0;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            width: 30%;
            background: #ffa726;
            border-radius: 2px;
            transition: all 0.3s ease;
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            margin-bottom: 24px;
        }
        
        .checkbox {
            margin-right: 12px;
            margin-top: 2px;
        }
        
        .checkbox-label {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .checkbox-label a {
            color: #667eea;
            text-decoration: none;
        }
        
        .register-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-bottom: 16px;
        }
        
        .register-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-link {
            text-align: center;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="header">
            <div class="title">创建账户</div>
            <div class="subtitle">开始管理你的小说人物关系</div>
        </div>
        
        <form>
            <div class="form-group">
                <label class="form-label">用户名</label>
                <input type="text" class="form-input" placeholder="请输入用户名">
            </div>
            
            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" class="form-input" placeholder="请输入邮箱地址">
            </div>
            
            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" class="form-input" placeholder="请输入密码">
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill"></div>
                    </div>
                    <span>密码强度：中等</span>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">确认密码</label>
                <input type="password" class="form-input" placeholder="请再次输入密码">
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" class="checkbox" id="agree">
                <label for="agree" class="checkbox-label">
                    我已阅读并同意<a href="#">用户协议</a>和<a href="#">隐私政策</a>
                </label>
            </div>
            
            <button type="submit" class="register-btn">注册</button>
        </form>
        
        <div class="login-link">
            <a href="#">已有账户？立即登录</a>
        </div>
    </div>
</body>
</html>
