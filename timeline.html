<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间线 - 修仙传说</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e1e5e9;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .back-btn {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #667eea;
        }
        
        .title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .add-btn {
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .filters {
            padding: 16px 20px;
            background: white;
            border-bottom: 1px solid #e1e5e9;
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .filter-tab {
            padding: 8px 16px;
            background: #f1f5f9;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .timeline-container {
            padding: 20px;
            position: relative;
        }
        
        .timeline-line {
            position: absolute;
            left: 40px;
            top: 20px;
            bottom: 20px;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            border-radius: 1px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 24px;
            padding-left: 60px;
        }
        
        .timeline-dot {
            position: absolute;
            left: -8px;
            top: 8px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #667eea;
        }
        
        .timeline-dot.critical {
            background: #ef4444;
            box-shadow: 0 0 0 2px #ef4444;
        }
        
        .timeline-dot.important {
            background: #f59e0b;
            box-shadow: 0 0 0 2px #f59e0b;
        }
        
        .timeline-dot.normal {
            background: #667eea;
            box-shadow: 0 0 0 2px #667eea;
        }
        
        .timeline-dot.minor {
            background: #94a3b8;
            box-shadow: 0 0 0 2px #94a3b8;
        }
        
        .event-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .event-card:hover {
            transform: translateY(-2px);
        }
        
        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .event-chapter {
            font-size: 12px;
            color: #667eea;
            font-weight: 500;
        }
        
        .event-menu {
            background: none;
            border: none;
            font-size: 16px;
            color: #999;
            cursor: pointer;
        }
        
        .event-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .event-description {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
        }
        
        .event-characters {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }
        
        .character-tag {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: #f1f5f9;
            border-radius: 12px;
            font-size: 12px;
            color: #666;
        }
        
        .character-avatar-small {
            width: 16px;
            height: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            color: white;
            font-size: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .event-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
        }
        
        .importance-badge {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .importance-critical {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .importance-important {
            background: #fef3c7;
            color: #d97706;
        }
        
        .importance-normal {
            background: #dbeafe;
            color: #2563eb;
        }
        
        .importance-minor {
            background: #f1f5f9;
            color: #64748b;
        }
        
        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
            transition: transform 0.2s ease;
        }
        
        .fab:hover {
            transform: scale(1.1);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-top">
            <div class="header-left">
                <button class="back-btn">←</button>
                <h1 class="title">时间线</h1>
            </div>
            <button class="add-btn">+</button>
        </div>
    </div>
    
    <div class="filters">
        <div class="filter-tabs">
            <button class="filter-tab active">全部</button>
            <button class="filter-tab">关键</button>
            <button class="filter-tab">重要</button>
            <button class="filter-tab">普通</button>
            <button class="filter-tab">次要</button>
        </div>
    </div>
    
    <div class="timeline-container">
        <div class="timeline-line"></div>
        
        <div class="timeline-item">
            <div class="timeline-dot critical"></div>
            <div class="event-card">
                <div class="event-header">
                    <div class="event-chapter">第5章</div>
                    <button class="event-menu">⋮</button>
                </div>
                <div class="event-title">大战魔教教主</div>
                <div class="event-description">李逍遥与王魔头展开最终决战，经过激烈的战斗，最终战胜了邪恶势力，拯救了苍生。</div>
                <div class="event-characters">
                    <div class="character-tag">
                        <div class="character-avatar-small">李</div>
                        <span>李逍遥</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">王</div>
                        <span>王魔头</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">赵</div>
                        <span>赵灵儿</span>
                    </div>
                </div>
                <div class="event-meta">
                    <span>2小时前</span>
                    <div class="importance-badge importance-critical">关键</div>
                </div>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-dot important"></div>
            <div class="event-card">
                <div class="event-header">
                    <div class="event-chapter">第4章</div>
                    <button class="event-menu">⋮</button>
                </div>
                <div class="event-title">获得神器</div>
                <div class="event-description">在古老的遗迹中，李逍遥找到了传说中的神器"轩辕剑"，实力大增。</div>
                <div class="event-characters">
                    <div class="character-tag">
                        <div class="character-avatar-small">李</div>
                        <span>李逍遥</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">张</div>
                        <span>张真人</span>
                    </div>
                </div>
                <div class="event-meta">
                    <span>1天前</span>
                    <div class="importance-badge importance-important">重要</div>
                </div>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-dot normal"></div>
            <div class="event-card">
                <div class="event-header">
                    <div class="event-chapter">第3章</div>
                    <button class="event-menu">⋮</button>
                </div>
                <div class="event-title">初入仙门</div>
                <div class="event-description">李逍遥通过考验，正式成为青云门弟子，开始系统学习修仙功法。</div>
                <div class="event-characters">
                    <div class="character-tag">
                        <div class="character-avatar-small">李</div>
                        <span>李逍遥</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">张</div>
                        <span>张真人</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">林</div>
                        <span>林月如</span>
                    </div>
                </div>
                <div class="event-meta">
                    <span>2天前</span>
                    <div class="importance-badge importance-normal">普通</div>
                </div>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-dot normal"></div>
            <div class="event-card">
                <div class="event-header">
                    <div class="event-chapter">第2章</div>
                    <button class="event-menu">⋮</button>
                </div>
                <div class="event-title">遇见师父</div>
                <div class="event-description">在山中偶遇隐世高人张真人，被其收为弟子，开始修仙之路。</div>
                <div class="event-characters">
                    <div class="character-tag">
                        <div class="character-avatar-small">李</div>
                        <span>李逍遥</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">张</div>
                        <span>张真人</span>
                    </div>
                </div>
                <div class="event-meta">
                    <span>3天前</span>
                    <div class="importance-badge importance-normal">普通</div>
                </div>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-dot minor"></div>
            <div class="event-card">
                <div class="event-header">
                    <div class="event-chapter">第1章</div>
                    <button class="event-menu">⋮</button>
                </div>
                <div class="event-title">平凡的开始</div>
                <div class="event-description">李逍遥还是一个普通的少年，在客栈帮忙，过着平凡的生活。</div>
                <div class="event-characters">
                    <div class="character-tag">
                        <div class="character-avatar-small">李</div>
                        <span>李逍遥</span>
                    </div>
                    <div class="character-tag">
                        <div class="character-avatar-small">小</div>
                        <span>小二</span>
                    </div>
                </div>
                <div class="event-meta">
                    <span>1周前</span>
                    <div class="importance-badge importance-minor">次要</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="height: 80px;"></div>
    
    <button class="fab">+</button>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
