<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制面板 - 小说人物关系管理器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-left h1 {
            font-size: 20px;
            font-weight: 600;
        }
        
        .header-left p {
            font-size: 14px;
            opacity: 0.9;
            margin-top: 4px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            padding: 20px;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        
        .stat-icon {
            font-size: 20px;
            margin-bottom: 12px;
        }
        
        .quick-actions {
            padding: 0 20px 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .action-btn {
            background: white;
            border: none;
            border-radius: 12px;
            padding: 20px 16px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .action-text {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .recent-activity {
            padding: 0 20px 20px;
        }
        
        .activity-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            padding: 16px 20px;
            border-bottom: 1px solid #f1f5f9;
            display: flex;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            background: #f1f5f9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #666;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <h1>欢迎回来，张三</h1>
            <p>继续你的创作之旅</p>
        </div>
        <div class="avatar">👤</div>
    </div>
    
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">📚</div>
            <div class="stat-number">5</div>
            <div class="stat-label">小说作品</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-number">23</div>
            <div class="stat-label">角色数量</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🔗</div>
            <div class="stat-number">45</div>
            <div class="stat-label">关系连接</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">📝</div>
            <div class="stat-number">12</div>
            <div class="stat-label">笔记数量</div>
        </div>
    </div>
    
    <div class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-grid">
            <button class="action-btn">
                <span class="action-icon">➕</span>
                <span class="action-text">创建小说</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">👤</span>
                <span class="action-text">添加角色</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">🔗</span>
                <span class="action-text">建立关系</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">📝</span>
                <span class="action-text">写笔记</span>
            </button>
        </div>
    </div>
    
    <div class="recent-activity">
        <h2 class="section-title">最近活动</h2>
        <div class="activity-list">
            <div class="activity-item">
                <div class="activity-icon">👤</div>
                <div class="activity-content">
                    <div class="activity-text">添加了角色"李小明"</div>
                    <div class="activity-time">2小时前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon">🔗</div>
                <div class="activity-content">
                    <div class="activity-text">建立了"张三"和"李四"的朋友关系</div>
                    <div class="activity-time">5小时前</div>
                </div>
            </div>
            <div class="activity-item">
                <div class="activity-icon">📚</div>
                <div class="activity-content">
                    <div class="activity-text">创建了新小说"都市传说"</div>
                    <div class="activity-time">1天前</div>
                </div>
            </div>
        </div>
    </div>
    
    <div style="height: 80px;"></div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item active">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
