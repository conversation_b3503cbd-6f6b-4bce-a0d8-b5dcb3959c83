<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修仙传说 - 小说详情</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .header {
            position: relative;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .back-btn {
            position: absolute;
            top: 16px;
            left: 16px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 8px;
            width: 36px;
            height: 36px;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }
        
        .edit-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255,255,255,0.2);
            border: none;
            border-radius: 8px;
            width: 36px;
            height: 36px;
            color: white;
            font-size: 16px;
            cursor: pointer;
        }
        
        .novel-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .novel-info {
            background: white;
            margin: -40px 20px 20px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            position: relative;
            z-index: 2;
        }
        
        .novel-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .novel-status {
            display: inline-block;
            padding: 4px 12px;
            background: #e7f3ff;
            color: #1976d2;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        .novel-description {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-around;
            padding: 16px 0;
            border-top: 1px solid #f1f5f9;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        
        .quick-actions {
            padding: 0 20px 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }
        
        .action-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .action-btn {
            background: white;
            border: none;
            border-radius: 12px;
            padding: 20px 16px;
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        .action-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
        }
        
        .action-text {
            font-size: 14px;
            font-weight: 500;
            color: #333;
        }
        
        .characters-preview {
            padding: 0 20px 20px;
        }
        
        .characters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .view-all-btn {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }
        
        .characters-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }
        
        .character-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .character-avatar {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .character-name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .character-role {
            font-size: 12px;
            color: #666;
        }
        
        .timeline-preview {
            padding: 0 20px 20px;
        }
        
        .timeline-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .timeline-date {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }
        
        .timeline-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .timeline-description {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            padding: 8px 0;
        }
        
        .nav-item {
            flex: 1;
            text-align: center;
            padding: 8px;
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        
        .nav-item.active {
            color: #667eea;
        }
        
        .nav-icon {
            font-size: 20px;
            display: block;
            margin-bottom: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <button class="back-btn">←</button>
        <button class="edit-btn">✏️</button>
        <div class="novel-icon">📖</div>
    </div>
    
    <div class="novel-info">
        <h1 class="novel-title">修仙传说</h1>
        <div class="novel-status">进行中</div>
        <p class="novel-description">
            一个普通少年的修仙之路，充满了挑战与机遇。在这个充满神秘力量的世界中，主人公将面对各种强敌，结识志同道合的伙伴，最终踏上成仙之路。
        </p>
        <div class="stats-row">
            <div class="stat-item">
                <div class="stat-number">8</div>
                <div class="stat-label">角色</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">15</div>
                <div class="stat-label">关系</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">事件</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5</div>
                <div class="stat-label">笔记</div>
            </div>
        </div>
    </div>
    
    <div class="quick-actions">
        <h2 class="section-title">快速操作</h2>
        <div class="action-grid">
            <button class="action-btn">
                <span class="action-icon">🕸️</span>
                <span class="action-text">关系图谱</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">⏰</span>
                <span class="action-text">时间线</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">👤</span>
                <span class="action-text">添加角色</span>
            </button>
            <button class="action-btn">
                <span class="action-icon">📝</span>
                <span class="action-text">写笔记</span>
            </button>
        </div>
    </div>
    
    <div class="characters-preview">
        <div class="characters-header">
            <h2 class="section-title">主要角色</h2>
            <a href="#" class="view-all-btn">查看全部</a>
        </div>
        <div class="characters-grid">
            <div class="character-card">
                <div class="character-avatar">李</div>
                <div class="character-name">李逍遥</div>
                <div class="character-role">主角</div>
            </div>
            <div class="character-card">
                <div class="character-avatar">赵</div>
                <div class="character-name">赵灵儿</div>
                <div class="character-role">女主</div>
            </div>
            <div class="character-card">
                <div class="character-avatar">林</div>
                <div class="character-name">林月如</div>
                <div class="character-role">配角</div>
            </div>
        </div>
    </div>
    
    <div class="timeline-preview">
        <div class="characters-header">
            <h2 class="section-title">最新事件</h2>
            <a href="#" class="view-all-btn">查看全部</a>
        </div>
        <div class="timeline-item">
            <div class="timeline-date">第3章</div>
            <div class="timeline-title">初入仙门</div>
            <div class="timeline-description">李逍遥通过考验，正式成为青云门弟子</div>
        </div>
        <div class="timeline-item">
            <div class="timeline-date">第2章</div>
            <div class="timeline-title">遇见师父</div>
            <div class="timeline-description">在山中偶遇隐世高人，开始修仙之路</div>
        </div>
    </div>
    
    <div style="height: 80px;"></div>
    
    <div class="bottom-nav">
        <a href="#" class="nav-item">
            <span class="nav-icon">🏠</span>
            首页
        </a>
        <a href="#" class="nav-item active">
            <span class="nav-icon">📚</span>
            小说
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">👥</span>
            角色
        </a>
        <a href="#" class="nav-item">
            <span class="nav-icon">⚙️</span>
            设置
        </a>
    </div>
</body>
</html>
